#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author : Tencent AI Arena
"""

import math
import numpy as np
from agent_ppo.feature.definition import (
    RelativeDistance,
    RelativeDirection,
    DirectionAngles,
    reward_process,
)

DIRS = [  # 0~7 八方向（x, y）
    (1, 0), (1, 1), (0, 1), (-1, 1),
    (-1, 0), (-1, -1), (0, -1), (1, -1)
]

# ─────────────────────────────────────────
# 2.  八射線距離 / 撞牆遮罩
# ─────────────────────────────────────────
def ray8_feature(local_map):
    H = local_map.shape[0]
    cx = cy = H // 2
    r_max = (H - 1) // 2

    dist = np.zeros(8, dtype=float)
    mask = np.zeros(8, dtype=bool)

    for a, (dx, dy) in enumerate(DIRS):
        step, x, y = 0, cx, cy
        while True:
            step += 1
            x += dx
            y += dy
            if not (0 <= x < H and 0 <= y < H):
                step -= 1
                break
            if local_map[y, x] == 0:
                step -= 1
                break
        dist[a] = step / r_max
        mask[a] = (step == 0)
    return dist, mask

# ─────────────────────────────────────────
# 3.  通用歸一化
# ─────────────────────────────────────────
def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)

# ─────────────────────────────────────────
# 4.  Preprocessor
# ─────────────────────────────────────────
class Preprocessor:
    def __init__(self):
        self.move_action_num = 16
        self.reset()

    # --------------- episode reset -----------------
    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0), dtype=float)
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        self.is_flashed = True
        self.bad_move_penalty = 0.0
        self.ray_dist = np.zeros(8)
        self.ray_mask = np.zeros(8, dtype=bool)
        self.talent_status = 0
        self.talent_cooldown = 0
        self.speed_up = 0
        self.buff_remain_time = 0
        self.feature_hero_status = np.zeros(6)
        self.explore_reward = 0.0

        self.treasure_flag = np.zeros(121, dtype=np.float32)
        self.end_flag = np.zeros(121, dtype=np.float32)
        self.obstacle_flag = np.zeros(121, dtype=np.float32)
        self.buff_flag = np.zeros(121, dtype=np.float32)

        self.global_memory_map = np.zeros((128, 128), dtype=np.float32)
        self.local_memory_map = np.zeros((11, 11), dtype=np.float32)
        self.memory_flag = np.zeros(121, dtype=np.float32)

        self.obj_status = np.zeros(15, dtype=np.float32)

        self.treasure_positions = [None] * 14
        self.treasure_found_flags = [False] * 14
        self.treasure_acquired_memory = [False] * 14

        self._obs = None
        self._extra_info = None
        self.obs = None
        self.extra_info = None

        # ★ 目前與上一個鎖定目標
        self.last_target = None      # {"config_id": int, "pos": (x, z)}
        self.target = None

    # ------------------------------------------------
    # 判斷目前鎖定目標是否仍有效
    # ------------------------------------------------
    def _is_target_still_valid(self) -> bool:
        if self.target is None:
            return False

        tid = self.target["config_id"]

        # 終點：必須確定位置
        if tid == 14:
            return self.end_pos is not None and self.is_end_pos_found

        # 寶箱 / Buff
        if not (0 <= tid <= 13):
            return False

        # 尚未在畫面中？（flag 消失代表伺服器說它不存在）
        if not self.treasure_found_flags[tid]:
            return False

        # 寶箱如果已被撿過就失效，Buff(0) 例外
        if tid != 0 and self.treasure_acquired_memory[tid]:
            return False

        return True

    # --------------- memory update -----------------
    def memory_update(self, cur_pos):
        x, z = cur_pos
        z = 127 - z
        self.global_memory_map[z, x] = min(1.0, self.global_memory_map[z, x] + 0.1)

        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = \
            self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    # --------------- internal helpers -----------------
    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        return np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            )
        )

    # --------------- pb2struct -----------------
    def pb2struct(self, frame_state, last_action):
        obs, extra_info = frame_state
        self.obs, self.extra_info = self._obs, self._extra_info
        self._obs, self._extra_info = obs, extra_info

        self.step_no = obs["frame_state"]["step_no"]
        hero = obs["frame_state"]["heroes"][0]
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])
        self.memory_update(self.cur_pos)

        # local map
        map_info = obs["map_info"]
        local_map = np.array(
            [[map_info[y]["values"][x] for x in range(11)] for y in range(11)],
            dtype=np.int8,
        )
        self.ray_dist, self.ray_mask = ray8_feature(local_map)

        # hero status
        self.talent_status = hero["talent"]["status"]
        self.talent_cooldown = hero["talent"]["cooldown"]
        self.speed_up = hero["speed_up"]
        self.buff_remain_time = hero["buff_remain_time"]
        self.is_flashed = self.talent_status == 1

        # ---------- 終點推測 ----------
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                elif not self.is_end_pos_found and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or getattr(self, "end_pos_dir", None) != end_pos_dir
                ):
                    d = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    dx, dz = d * math.cos(math.radians(theta)), d * math.sin(math.radians(theta))
                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + dx))),
                        max(0, min(128, round(self.cur_pos[1] + dz))),
                    )
                    self.end_pos_dir = end_pos_dir

        # ---------- 物件狀態 ----------
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 3:
                continue
            if organ["sub_type"] != 4 and organ["status"] == 1:
                self.obj_status[organ["config_id"]] = 1.0
            else:
                self.obj_status[14] = 1.0

        # ---------- 寶箱 / Buff ----------
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] in (1, 2):
                treasure_id = organ["config_id"]  # 0=Buff, 1~13=寶箱
                if 0 <= treasure_id <= 13:
                    t_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                    t_dir = RelativeDirection[organ["relative_pos"]["direction"]]

                    if organ["status"] == 1:  # 可撿取
                        self.treasure_found_flags[treasure_id] = True
                        self.treasure_positions[treasure_id] = (organ["pos"]["x"], organ["pos"]["z"])
                    elif organ["status"] == 0:  # 已撿取
                        if treasure_id != 0:      # Buff 允許重複撿
                            self.treasure_acquired_memory[treasure_id] = True
                        self.treasure_found_flags[treasure_id] = False
                        self.treasure_positions[treasure_id] = None
                    elif organ["status"] == -1 and not self.treasure_acquired_memory[treasure_id]:
                        self.treasure_found_flags[treasure_id] = True
                        if (self.treasure_positions[treasure_id] is None
                                or getattr(self, f"treasure_pos_dir_{treasure_id}", None) != t_dir):
                            d = t_dis * 20
                            theta = DirectionAngles[t_dir]
                            dx, dz = d * math.cos(math.radians(theta)), d * math.sin(math.radians(theta))
                            self.treasure_positions[treasure_id] = (
                                max(0, min(128, round(self.cur_pos[0] + dx))),
                                max(0, min(128, round(self.cur_pos[1] + dz))),
                            )
                            setattr(self, f"treasure_pos_dir_{treasure_id}", t_dir)

        # ---------- 目標排序 ----------
        treasure_features, treasure_distances = [], []
        for i in range(14):
            if (self.treasure_positions[i] is not None
                    and self.treasure_found_flags[i]
                    and (i == 0 or not self.treasure_acquired_memory[i])):
                feat = self._get_pos_feature(True, self.cur_pos, self.treasure_positions[i])
                treasure_features.append(feat)
                md = abs(self.cur_pos[0] - self.treasure_positions[i][0]) + \
                    abs(self.cur_pos[1] - self.treasure_positions[i][1])
                treasure_distances.append((md, i))
            else:
                treasure_features.append(np.zeros(6))
                treasure_distances.append((float('inf'), i))

        # 終點
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)
        treasure_features.append(self.feature_end_pos)
        md_end = float('inf')
        if self.end_pos is not None:
            md_end = abs(self.cur_pos[0] - self.end_pos[0]) + abs(self.cur_pos[1] - self.end_pos[1])
        treasure_distances.append((md_end, 14))

        # ──────────────────────────────
        # 按種類拆距離表
        # ──────────────────────────────
        box_list  = [(d, idx) for d, idx in treasure_distances
                    if 1 <= idx <= 13 and d != float('inf')]
        buff_info = next(((d, 0) for d, idx in treasure_distances
                        if idx == 0 and d != float('inf')), None)
        has_buff  = buff_info is not None
        box_list.sort()                      # 最近寶箱優先

        # ──────────────────────────────
        # 鎖定目標（只在舊目標失效時換）
        # ──────────────────────────────
        old_target = self.target
        if not self._is_target_still_valid():
            if box_list:                                 # 1) 仍有寶箱
                new_id  = box_list[0][1]
                self.target = {"config_id": new_id,
                            "pos": self.treasure_positions[new_id]}
            elif md_end != float('inf'):                 # 2) 無寶箱 → 終點
                self.target = {"config_id": 14, "pos": self.end_pos}
            elif has_buff:                               # 3) 無終點 → Buff
                self.target = {"config_id": 0, "pos": self.treasure_positions[0]}
            else:                                        # 4) 什麼都沒有
                self.target = None
        self.last_target = old_target                    # 每幀記錄上一目標

        # ──────────────────────────────
        # simple_feats 排序
        # ──────────────────────────────
        priority_idx = []
        if self.target is not None:
            priority_idx.append(self.target["config_id"])

        if box_list:                                     # ── 有寶箱情況
            for _, idx in box_list:
                if idx not in priority_idx:
                    priority_idx.append(idx)
                if len(priority_idx) == 3:
                    break
            if len(priority_idx) < 3 and has_buff and 0 not in priority_idx:
                priority_idx.append(0)                   # Buff
            if len(priority_idx) < 3 and md_end != float('inf') and 14 not in priority_idx:
                priority_idx.append(14)                  # End
        else:                                            # ── 沒寶箱情況
            if md_end != float('inf') and 14 not in priority_idx:
                priority_idx.append(14)                  # End
            if len(priority_idx) < 3 and has_buff and 0 not in priority_idx:
                priority_idx.append(0)                   # Buff

        while len(priority_idx) < 3:                     # 補零向量
            priority_idx.append(-1)

        # 組 simple_feats
        simple_feats = []
        for idx in priority_idx:
            if idx == -1:
                simple_feats.append(np.zeros(6))
            elif idx == 14:
                simple_feats.append(self.feature_end_pos)
            else:
                simple_feats.append(treasure_features[idx])

        first_hist = self.history_pos[0] if self.history_pos else self.cur_pos
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, first_hist)
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

        # ------ 各種 map flag ------
        treasure_map = np.zeros((11, 11), dtype=np.float32)
        end_map = np.zeros((11, 11), dtype=np.float32)
        obstacle_map = np.zeros((11, 11), dtype=np.float32)
        buff_map = np.zeros((11, 11), dtype=np.float32)

        for r, row in enumerate(map_info):
            for c, v in enumerate(row["values"]):
                if v == 4:
                    treasure_map[r, c] = 1.0
                elif v == 3:
                    end_map[r, c] = 1.0
                elif v == 0:
                    obstacle_map[r, c] = 1.0
                elif v == 6:
                    buff_map[r, c] = 1.0

        # 保持原有的展平版本用於向後兼容
        self.treasure_flag = treasure_map.flatten()
        self.end_flag = end_map.flatten()
        self.obstacle_flag = obstacle_map.flatten()
        self.buff_flag = buff_map.flatten()
        
        # 新增：產生 5×11×11 的 maps_tensor
        self.maps_tensor = np.stack([
            treasure_map,    # 寶箱地圖
            end_map,         # 終點地圖
            obstacle_map,    # 障礙物地圖
            buff_map,        # Buff地圖
            self.local_memory_map  # 記憶地圖
        ], axis=0)  # shape: (5, 11, 11)

        # hero status feature
        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_hero_status = np.array(
            [
                norm(hero["pos"]["x"], 128, -128),
                norm(hero["pos"]["z"], 128, -128),
                self.talent_status,
                norm(self.talent_cooldown, 100),
                self.speed_up,
                norm(self.buff_remain_time, 51),
            ],
            dtype=float,
        )

        self.last_action = last_action

        # 合法動作
        legal_action = self.get_legal_action()

        # one-hot 位置
        pos_row = [0] * 128
        pos_row[self.cur_pos[0]] = 1
        pos_col = [0] * 128
        pos_col[self.cur_pos[1]] = 1

        # 整合最終特徵（移除重複的 Flag，因為它們已經在 maps_tensor 中通過 CNN 處理）
        self.feature_all = np.concatenate(
            [
                self.cur_pos_norm,
                self.feature_hero_status,
                pos_row,
                pos_col,
                self.obj_status,
                np.concatenate(simple_feats),
                self.feature_history_pos,
                legal_action,
                # 移除重複的 Flag：treasure_flag, end_flag, obstacle_flag, buff_flag, memory_flag
                # 這些資訊已經在 maps_tensor 中通過 CNN 處理
            ],
            axis=0,
        )

    # --------------- legal action -----------------
    def get_legal_action(self):
        legal = [True] * self.move_action_num
        for i in range(8):
            if self.ray_mask[i]:
                legal[i] = False
        legal[8:] = [self.is_flashed] * 8

        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 1e-3
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 1e-3
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
            self.bad_move_penalty = -1
        else:
            self.bad_move_ids.clear()
            self.bad_move_penalty = 0

        for mid in self.bad_move_ids:
            legal[mid] = False
        return legal

    # --------------- public API -----------------
    def process(self, frame_state, last_action, is_exploit=False):
        self.pb2struct(frame_state, last_action)
        legal_action = self.get_legal_action()

        if is_exploit:
            return self.feature_all, self.maps_tensor, legal_action
        else:
            return (
                self.feature_all,
                self.maps_tensor,
                legal_action,
                reward_process(
                    self._obs,
                    self._extra_info,
                    self.obs,
                    self.extra_info,
                    self.local_memory_map,
                    self.last_target,
                    self.target,        # 鎖定後的最終目標
                ),
            )